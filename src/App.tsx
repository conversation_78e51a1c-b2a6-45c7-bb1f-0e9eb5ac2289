import React from 'react'
import './App.css'
import SimpleReactionDemo from './components/SimpleReactionDemo'

function App() {
  return (
    <div className="App">
      <header style={{ 
        background: 'linear-gradient(135deg, #43cea2 0%, #185a9d 100%)',
        color: 'white',
        padding: '2rem',
        borderRadius: '12px',
        marginBottom: '2rem',
        boxShadow: '0 4px 15px rgba(0,0,0,0.1)'
      }}>
        <h1 style={{ margin: 0, fontSize: '2.5rem' }}>🚀 MobX 测试项目</h1>
        <p style={{ margin: '0.5rem 0 0 0', opacity: 0.9 }}>探索状态管理的魅力 ✨</p>
      </header>
      
      <div style={{ 
        background: '#f8f9fa',
        padding: '1.5rem',
        borderRadius: '8px',
        border: '1px solid #e9ecef'
      }}>
        <h3 style={{ color: '#495057', marginTop: 0 }}>📊 实时数据演示</h3>
        <SimpleReactionDemo />
      </div>
      
      <footer style={{
        marginTop: '2rem',
        padding: '1rem',
        textAlign: 'center',
        color: '#6c757d',
        borderTop: '1px solid #dee2e6'
      }}>
        <small><b>💡 使用 MobX 进行响应式状态管理</b></small>
      </footer>
    </div>
  )
}

export default App
